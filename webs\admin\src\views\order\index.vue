<template>
  <div class="order-management">
    <CustomTable
      title="订单管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
      </template>

      <template #orderNo="{ row }">
        <el-button type="text" @click="handleViewOrder(row)">
          {{ row.orderNo }}
        </el-button>
      </template>

      <template #user="{ row }">
        <div class="user-info">
          <el-avatar :src="row.user?.avatar" :size="32">
            {{ row.user?.name ? row.user.name.charAt(0) : 'U' }}
          </el-avatar>
          <span class="ml-2">{{ row.user?.name || '未知用户' }}</span>
        </div>
      </template>

      <template #dishes="{ row }">
        <div class="dishes-info">
          <el-tag
            v-for="dish in row.dishes.slice(0, 2)"
            :key="dish.id"
            size="small"
            class="mr-1"
          >
            {{ dish.name }}
          </el-tag>
          <el-tag v-if="row.dishes.length > 2" size="small" type="info">
            +{{ row.dishes.length - 2 }}
          </el-tag>
        </div>
      </template>

      <template #amount="{ row }">
        <span class="amount-text">¥{{ row.amount }}</span>
      </template>

      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button size="small" @click="handleViewOrder(row)">查看</el-button>
        <el-button
          size="small"
          type="primary"
          @click="handleProcessOrder(row)"
          v-if="row.status === 'pending'"
        >
          处理
        </el-button>
        <el-button
          size="small"
          type="success"
          @click="handleCompleteOrder(row)"
          v-if="row.status === 'processing'"
        >
          完成
        </el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { orderApi } from '@/api/order'
import { formatTime, getOrderStatusType, getOrderStatusText } from '@/utils/common'
import dayjs from 'dayjs'

const loading = ref(false)
const tableData = ref([])

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  status: '',
  userPhone: '',
  orderNo: '',
  dateRange: []
})

// 表格列配置
const columns = [
  { prop: 'orderNo', label: '订单号', width: 160, slot: true },
  { prop: 'user', label: '用户', width: 150, slot: true },
  { prop: 'dishes', label: '订单菜品', minWidth: 200, slot: true },
  { prop: 'amount', label: '订单金额', width: 120, slot: true },
  { prop: 'status', label: '订单状态', width: 100, slot: true },
  { prop: 'createdAt', label: '下单时间', width: 160, formatter: (row) => formatTime(row.createdAt) },
  { label: '操作', width: 180, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'orderNo', label: '订单号', type: 'input' },
  { prop: 'userPhone', label: '用户手机号', type: 'input' },
  { prop: 'status', label: '订单状态', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '待处理', value: 'pending' },
    { label: '处理中', value: 'processing' },
    { label: '已完成', value: 'completed' },
    { label: '已取消', value: 'cancelled' }
  ]},
  { prop: 'dateRange', label: '下单时间', type: 'daterange' }
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据
    tableData.value = generateMockData()
    pagination.total = 50
  } catch (error) {
    console.error('加载订单数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const data = []
  const users = [
    { id: 1, name: '张三', phone: '13800138001', avatar: 'https://picsum.photos/100/100?random=1' },
    { id: 2, name: '李四', phone: '13800138002', avatar: 'https://picsum.photos/100/100?random=2' },
    { id: 3, name: '王五', phone: '13800138003', avatar: 'https://picsum.photos/100/100?random=3' }
  ]

  const dishes = [
    { id: 1, name: '红烧肉', price: 28, quantity: 1 },
    { id: 2, name: '宫保鸡丁', price: 26, quantity: 1 },
    { id: 3, name: '清炒时蔬', price: 16, quantity: 1 },
    { id: 4, name: '米饭', price: 3, quantity: 2 }
  ]

  const statuses = ['pending', 'processing', 'completed', 'cancelled']

  for (let i = 0; i < 10; i++) {
    const orderDishes = dishes.slice(0, Math.floor(Math.random() * 3) + 2)
    const amount = orderDishes.reduce((sum, dish) => sum + dish.price * dish.quantity, 0)

    data.push({
      id: i + 1,
      orderNo: `ORD${dayjs().format('YYYYMMDD')}${String(i + 1).padStart(3, '0')}`,
      user: users[i % users.length],
      dishes: orderDishes,
      amount: amount.toFixed(2),
      status: statuses[i % statuses.length],
      remark: i % 3 === 0 ? '少放辣椒' : '',
      createdAt: dayjs().subtract(Math.floor(Math.random() * 30), 'day').toDate()
    })
  }
  return data
}

const getStatusType = (status) => getOrderStatusType(status)
const getStatusText = (status) => getOrderStatusText(status)

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = ''
    }
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleExport = () => {
  try {
    // 模拟导出功能
    const csvContent = generateCSV()
    downloadCSV(csvContent, `订单报表_${dayjs().format('YYYY-MM-DD')}.csv`)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const generateCSV = () => {
  const headers = ['订单号', '用户姓名', '用户手机', '订单金额', '订单状态', '下单时间']
  const rows = tableData.value.map(row => [
    row.orderNo,
    row.user?.name || '未知用户',
    row.user?.phone || '未知手机号',
    row.amount,
    getStatusText(row.status),
    formatTime(row.createdAt)
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return csvContent
}

const downloadCSV = (content, filename) => {
  const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const handleViewOrder = (row) => {
  ElMessage.info(`查看订单：${row.orderNo}`)
}

const handleProcessOrder = async (row) => {
  try {
    await ElMessageBox.confirm('确定要处理这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    ElMessage.success('订单已开始处理')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('处理订单失败')
    }
  }
}

const handleCompleteOrder = async (row) => {
  try {
    await ElMessageBox.confirm('确定要完成这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })

    ElMessage.success('订单已完成')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('完成订单失败')
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.order-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

.user-info {
  @apply flex items-center;
}

.dishes-info {
  @apply flex flex-wrap gap-1;
}

.amount-text {
  @apply text-green-600 font-semibold;
}
</style>
